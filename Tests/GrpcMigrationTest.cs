using System;
using System.Threading.Tasks;
using HeroYulgang.Services;
using RxjhServer;

namespace HeroYulgang.Tests
{
    /// <summary>
    /// Test class để verify GRPC migration từ TCP
    /// </summary>
    public class GrpcMigrationTest
    {
        private GrpcConnect? _grpcConnect;
        private GameServerCommunicationClient? _grpcClient;

        /// <summary>
        /// Test khởi tạo GRPC connection
        /// </summary>
        public async Task<bool> TestGrpcConnectionAsync()
        {
            try
            {
                Console.WriteLine("=== GRPC Migration Test ===");
                Console.WriteLine("1. Testing GRPC Connection...");

                _grpcClient = GameServerCommunicationClient.Instance;
                var connected = await _grpcClient.ConnectAsync();

                if (connected)
                {
                    Console.WriteLine("✓ GRPC Connection successful");
                    return true;
                }
                else
                {
                    Console.WriteLine("✗ GRPC Connection failed");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ GRPC Connection error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Test GrpcConnect class (thay thế cho Connect.cs)
        /// </summary>
        public async Task<bool> TestGrpcConnectAsync()
        {
            try
            {
                Console.WriteLine("2. Testing GrpcConnect class...");

                _grpcConnect = new GrpcConnect();
                var setupResult = await _grpcConnect.SetupAsync();

                if (setupResult)
                {
                    Console.WriteLine("✓ GrpcConnect setup successful");
                    return true;
                }
                else
                {
                    Console.WriteLine("✗ GrpcConnect setup failed");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ GrpcConnect setup error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Test gửi message qua GRPC (thay thế cho Transmit)
        /// </summary>
        public async Task<bool> TestTransmitAsync()
        {
            try
            {
                Console.WriteLine("3. Testing GRPC Transmit...");

                if (_grpcConnect == null)
                {
                    Console.WriteLine("✗ GrpcConnect not initialized");
                    return false;
                }

                // Test gửi message SERVER_CONNECT_LOGIN_X
                var connectMessage = $"SERVER_CONNECT_LOGIN_X|{RxjhServer.World.ServerID}|{RxjhServer.World.MaximumOnline}|12345";
                var result = await _grpcConnect.TransmitAsync(connectMessage);

                if (result)
                {
                    Console.WriteLine("✓ GRPC Transmit successful");
                    return true;
                }
                else
                {
                    Console.WriteLine("✗ GRPC Transmit failed");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ GRPC Transmit error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Test Cross Server Action
        /// </summary>
        public async Task<bool> TestCrossServerActionAsync()
        {
            try
            {
                Console.WriteLine("4. Testing Cross Server Action...");

                if (_grpcConnect == null)
                {
                    Console.WriteLine("✗ GrpcConnect not initialized");
                    return false;
                }

                // Test gửi Cross Server Zone Action
                _grpcConnect.SendCrossServerAction(1, "TEST_ACTION", 12345, "param1", "param2");
                
                Console.WriteLine("✓ Cross Server Action sent successfully");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Cross Server Action error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Test Cross Server Broadcast
        /// </summary>
        public async Task<bool> TestCrossServerBroadcastAsync()
        {
            try
            {
                Console.WriteLine("5. Testing Cross Server Broadcast...");

                if (_grpcConnect == null)
                {
                    Console.WriteLine("✗ GrpcConnect not initialized");
                    return false;
                }

                // Test gửi Cross Server Zone Broadcast
                _grpcConnect.SendCrossServerBroadcast(1, "TEST_BROADCAST", 12345, "AA5516002C01121708000000000000000000000000000000558D55AA", 100, 200);
                
                Console.WriteLine("✓ Cross Server Broadcast sent successfully");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Cross Server Broadcast error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Test với response data
        /// </summary>
        public async Task<bool> TestTransmitWithResponseAsync()
        {
            try
            {
                Console.WriteLine("6. Testing GRPC Transmit with Response...");

                if (_grpcConnect == null)
                {
                    Console.WriteLine("✗ GrpcConnect not initialized");
                    return false;
                }

                // Test gửi message GET_SERVER_LIST
                var result = await _grpcConnect.TransmitWithResponseAsync("GET_SERVER_LIST|1");

                if (result.Success)
                {
                    Console.WriteLine($"✓ GRPC Transmit with Response successful. Response data count: {result.ResponseData.Length}");
                    foreach (var data in result.ResponseData)
                    {
                        Console.WriteLine($"  - Response: {data}");
                    }
                    return true;
                }
                else
                {
                    Console.WriteLine("✗ GRPC Transmit with Response failed");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ GRPC Transmit with Response error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Chạy tất cả tests
        /// </summary>
        public async Task<bool> RunAllTestsAsync()
        {
            try
            {
                Console.WriteLine("Starting GRPC Migration Tests...\n");

                var tests = new[]
                {
                    TestGrpcConnectionAsync(),
                    TestGrpcConnectAsync(),
                    TestTransmitAsync(),
                    TestCrossServerActionAsync(),
                    TestCrossServerBroadcastAsync(),
                    TestTransmitWithResponseAsync()
                };

                var results = await Task.WhenAll(tests);
                
                var passedCount = 0;
                var totalCount = results.Length;

                for (int i = 0; i < results.Length; i++)
                {
                    if (results[i])
                        passedCount++;
                }

                Console.WriteLine($"\n=== Test Results ===");
                Console.WriteLine($"Passed: {passedCount}/{totalCount}");
                Console.WriteLine($"Success Rate: {(passedCount * 100.0 / totalCount):F1}%");

                if (passedCount == totalCount)
                {
                    Console.WriteLine("🎉 All tests passed! GRPC migration is working correctly.");
                    return true;
                }
                else
                {
                    Console.WriteLine("⚠️  Some tests failed. Please check the implementation.");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Test suite error: {ex.Message}");
                return false;
            }
            finally
            {
                // Cleanup
                if (_grpcConnect != null)
                {
                    _grpcConnect.Dispose();
                }

                if (_grpcClient != null)
                {
                    _grpcClient.Dispose();
                }
            }
        }

        /// <summary>
        /// Test compatibility với code cũ
        /// </summary>
        public void TestBackwardCompatibility()
        {
            try
            {
                Console.WriteLine("7. Testing Backward Compatibility...");

                if (_grpcConnect == null)
                {
                    Console.WriteLine("✗ GrpcConnect not initialized");
                    return;
                }

                // Test các method cũ vẫn hoạt động
                _grpcConnect.Transmit("TEST_MESSAGE|param1|param2");
                _grpcConnect.ReviewUserLogin();
                _grpcConnect.Sestup();

                Console.WriteLine("✓ Backward compatibility methods work correctly");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Backward compatibility error: {ex.Message}");
            }
        }
    }
}
