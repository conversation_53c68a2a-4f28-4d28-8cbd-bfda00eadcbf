syntax = "proto3";

option csharp_namespace = "HeroYulgang.Protos";

package gameserver_communication;

// Dịch vụ giao tiếp giữa GameServer và LoginServer
service GameServerCommunication {
  // Gửi message từ GameServer đến LoginServer
  rpc SendMessage (GameServerMessageRequest) returns (GameServerMessageResponse);
  
  // Gửi message với response data
  rpc SendMessageWithResponse (GameServerMessageRequest) returns (GameServerMessageWithDataResponse);
  
  // Stream để nhận messages từ LoginServer
  rpc ReceiveMessages (GameServerStreamRequest) returns (stream GameServerMessageResponse);
}

// Yêu cầu gửi message từ GameServer
message GameServerMessageRequest {
  int32 server_id = 1;
  string server_name = 2;
  string message_type = 3;
  repeated string parameters = 4;
  string raw_message = 5; // Message gốc dạng string để backward compatibility
  int64 timestamp = 6;
}

// Phản hồi đơn giản
message GameServerMessageResponse {
  bool success = 1;
  string message = 2;
  string error_code = 3;
}

// Phản hồi có kèm data
message GameServerMessageWithDataResponse {
  bool success = 1;
  string message = 2;
  string error_code = 3;
  repeated string response_data = 4;
  string response_type = 5;
}

// Yêu cầu stream
message GameServerStreamRequest {
  int32 server_id = 1;
  string server_name = 2;
}

// Message types được hỗ trợ
enum MessageType {
  UNKNOWN = 0;
  CROSS_SERVER_ZONE_INFO = 1;
  CROSS_SERVER_ZONE_REMOVE = 2;
  CROSS_SERVER_ZONE_ACTION = 3;
  CROSS_SERVER_ZONE_BROADCAST = 4;
  CROSS_SERVER_ZONE_BROADCAST_FULL = 5;
  CROSS_SERVER_ZONE_NPC_BROADCAST = 6;
  CROSS_SERVER_ZONE_NPC_BROADCAST_FULL = 7;
  GROUP_QUEST_MESSAGE = 8;
  CHAT_GUILD = 9;
  OPEN_TREASURE = 10;
  DECREASE_FACTION_WAR = 11;
  LION_ROAR = 12;
  LION_ROARX = 13;
  GET_SERVER_LIST = 14;
  RDISCONNECTED_FACTION = 15;
  TRANSMISSION_MESSAGE = 16;
  PK_MESSAGE = 17;
  COUPLE_MESSAGE = 18;
  UPDATE_CONFIGURATION_X = 19;
  FACTION_WAR_PROGRESS = 20;
  FACTION_WAR_TOTAL = 21;
  GUILD_MESSAGE = 22;
  PVP = 23;
  MONSTER_DROP = 24;
  USER_KICKOUT = 25;
  ACCOUNT_SERVER_DISCONNECTED = 26;
  CHANGE_LINE_LOGIN = 27;
  USER_LOGIN_X = 28;
  OP_CLIENT = 29;
  SEND_ANNOUCEMENT = 30;
  USER_KICKOUTID = 31;
  GROUP_QUEST_CONTRIBUTION = 32;
  GROUP_QUEST_COMPLETE = 33;
  GROUP_QUEST_PROGRESS = 34;
  MAILCOD_ADMIN_NOTI = 35;
  REVIEW_USER_LOGIN = 36;
  SERVER_CONNECT_LOGIN_X = 37;
  UPDATE_SERVER_PORT = 38;
}
