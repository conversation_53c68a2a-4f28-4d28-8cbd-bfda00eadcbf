using System;
using Akka.Actor;
using Akka.IO;
using HeroYulgang.Core.Network;
using HeroYulgang.Helpers;
using HeroYulgang.Services;
using RxjhServer;

namespace HeroYulgang.Core.Actors
{
    /// <summary>
    /// Actor x<PERSON> lý kết nối của một client cụ thể
    /// </summary>
    public class ClientActor : ReceiveActor
    {
        private readonly IActorRef _connection;
        private readonly ClientSession _session;
        private readonly IActorRef _packetHandlerActor;
        private IActorRef _playerPacketHandlerActor;
        private Players _player;
        private ActorNetState _actorNetState;

        public ClientActor(IActorRef connection, ClientSession session, IActorRef packetHandlerActor)
        {
            _connection = connection;
            _session = session;
            _packetHandlerActor = packetHandlerActor;

            // Tạo ActorNetState cho kết nối này
            _actorNetState = PlayerNetworkManager.CreateActorNetState(_connection, _session.SessionId, _session.RemoteEndPoint);

            // <PERSON><PERSON><PERSON> nghĩa các message handler
            Receive<Tcp.Received>(msg => HandleReceived(msg));
            Receive<Tcp.ConnectionClosed>(msg => HandleConnectionClosed(msg));
            Receive<SetPlayerReference>(msg => SetPlayerReference(msg));
        }

        private void SetPlayerReference(SetPlayerReference message)
        {
            _player = message.Player;

            // Tạo PlayerPacketHandlerActor khi có tham chiếu đến Player
            if (_player != null && _playerPacketHandlerActor == null && _actorNetState != null)
            {
                // Cập nhật Client của Player để sử dụng ActorNetState
                PlayerNetworkManager.UpdatePlayerClient(_player, _actorNetState);

                // Tạo PlayerPacketHandlerActor
                _playerPacketHandlerActor = Context.ActorOf(
                    Props.Create(() => new PlayerPacketHandlerActor(_player, _connection)),
                    $"player-packet-handler-{_session.SessionId}");

                Logger.Instance.Debug($"Đã tạo PlayerPacketHandlerActor cho người chơi {_player.UserName}");
            }
        }

        private void HandleReceived(Tcp.Received received)
        {
            try
            {
                // Cập nhật thời gian hoạt động
                _session.UpdateActivity();

                // Chuyển đổi ByteString thành byte array
                byte[] data = received.Data.ToArray();
                byte[] decryptedData = Utils.Crypto.DecryptPacket(data);

                // Ghi log gói tin nhận được nếu cần
                //PacketLogger.LogIncomingPacket(_session.SessionId, data);

                // Nếu đã có PlayerPacketHandlerActor, gửi packet đến đó để xử lý
                if (_playerPacketHandlerActor != null && _player != null)
                {
                    Logger.Instance.Debug($"Chuyển tiếp packet đến PlayerPacketHandlerActor cho session {_session.SessionId}");
                    
                    _playerPacketHandlerActor.Tell(new ProcessPlayerPacket(decryptedData, decryptedData.Length));
                }
                else
                {
                    // Nếu chưa có PlayerPacketHandlerActor, gửi đến PacketHandlerActor mặc định
                    Logger.Instance.Debug($"Chuyển tiếp packet đến PacketHandlerActor vì _player hoặc _playerPacketHandlerActor là null (session {_session.SessionId})");
                    
                    _packetHandlerActor.Tell(new ProcessPacket(_connection, _session, decryptedData));
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi xử lý dữ liệu nhận được từ client {_session.SessionId}: {ex.Message}");
            }
        }

        private void HandleConnectionClosed(Tcp.ConnectionClosed message)
        {
            // Kết nối đã đóng, dừng actor
            Context.Stop(Self);
        }
    }

    /// <summary>
    /// Message để thiết lập tham chiếu đến đối tượng Player
    /// </summary>
    public class SetPlayerReference
    {
        public Players Player { get; }

        public SetPlayerReference(Players player)
        {
            Player = player;
        }
    }

    /// <summary>
    /// Message yêu cầu xử lý gói tin
    /// </summary>
    public class ProcessPacket
    {
        public IActorRef Connection { get; }
        public ClientSession Session { get; }
        public byte[] Data { get; }

        public ProcessPacket(IActorRef connection, ClientSession session, byte[] data)
        {
            Connection = connection;
            Session = session;
            Data = data;
        }
    }
}
