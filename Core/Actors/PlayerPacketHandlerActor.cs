using System;
using System.Collections.Generic;
using Akka.Actor;
using HeroYulgang.Helpers;
using HeroYulgang.Services;
using RxjhServer;
using RxjhServer.HelperTools;

namespace HeroYulgang.Core.Actors
{
    /// <summary>
    /// Actor x<PERSON> lý các gói tin từ client dành riêng cho người chơi
    /// </summary>
    public class PlayerPacketHandlerActor : ReceiveActor
    {
        private readonly Players _player;
        private readonly IActorRef _clientActor;

        public PlayerPacketHandlerActor(Players player, IActorRef clientActor)
        {
            _player = player;
            _clientActor = clientActor;

            // Đ<PERSON>ng ký message handler
            Receive<ProcessPlayerPacket>(HandlePlayerPacket);
        }

        private void HandlePlayerPacket(ProcessPlayerPacket message)
        {
            try
            {
                // Xử lý packet
                ProcessPacket(message.Data, message.Length);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi xử lý gói tin từ người chơi {_player.UserName}: {ex.Message}");
            }
        }

        /// <summary>
        /// Xử lý packet từ client
        /// </summary>
        /// <param name="clientsend">D<PERSON> liệu packet</param>
        /// <param name="length">Độ dài packet</param>
        private void ProcessPacket(byte[] clientsend, int length)
        {
            length -= 2;
            var data = new byte[length];
            System.Buffer.BlockCopy(clientsend, 0, data, 0, 6);
            System.Buffer.BlockCopy(clientsend, 8, data, 6, data.Length - 6);
            int packetType = BitConverter.ToInt16(data, 6);
            int packetData = BitConverter.ToInt16(data, 9);
            int packetCase = BitConverter.ToInt16(data, 10);

            // Log packet nếu cần
            if (ConfigManager.Instance.AppSettings.Environment.Equals("Development", StringComparison.OrdinalIgnoreCase) && packetType != 176 && packetType != 7 && packetType != 9)
            {
                LogHelper.WriteLine(LogLevel.Debug, $"packetnum: [{packetType}] - case:[{packetCase}] - CharID: [{_player.SessionID}] - Data:[{packetData}] : {Converter.ToString(data)}");
            }

            try
            {
                // Kiểm tra xem người chơi đã kết nối chưa
                if (!World.allConnectedChars.TryGetValue(_player.SessionID, out var _))
                {
                    HandlePreLoginPackets(packetType, data, length);
                    return;
                }

                // Xử lý heartbeat riêng
                if (packetType == 176)
                {
                    _player.Phat_Hien_Nhip_Tim(data, length);
                    return;
                }

                // Kiểm tra kết nối thành công
                if (!_player._connectionSucceeded)
                {
                    throw new Exception("Connection not succeeded");
                }

                // Xử lý các loại packet khác
                HandleGamePackets(packetType, data, length);

                // Cập nhật trạng thái di chuyển
                if (packetType != 7)
                {
                    _player.NhanVatDangDiChuyen = false;
                }

                // Xử lý xác nhận tấn công
                if (packetType != 16666 && length != 36 && packetType == 9 && _player.ThoiGianXacNhanTanCong != 0)
                {
                    _player.AttackConfirmation_Apply++;
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Manage Packet() Lỗi tại case: [{packetType}]-[{_player.Client.WorldId}]-[{_player.Client}] [{ex}]");
                Console.WriteLine(ex);
                _player.Client.Dispose();
                logo.logdis($"Disconnected![{_player.Userid}]-[{_player.UserName}][Mã dis 10]");
            }
        }

        /// <summary>
        /// Xử lý các packet trước khi đăng nhập
        /// </summary>
        private void HandlePreLoginPackets(int packetType, byte[] data, int length)
        {
            switch (packetType)
            {
                case 20:
                    _player.CreateCharacter(data, length);
                    break;
                case 16:
                    _player.GetAListOfPeople(data, length);
                    break;
                case 1:
                    _player.KetNoi_DangNhap(data, length);
                    break;
                case 3:
                    _player.DangXuat(data, length);
                    break;
                case 5:
                    _player.CharacterLogin(data, length);
                    break;
                case 143:
                    _player.Display();
                    break;
                case 56:
                    _player.KiemTraNhanVat_CoTonTaiHayKhong(data, length);
                    break;
                case 30:
                    _player.XoaBoNhanVat(data, length);
                    break;
                case 836:
                    _player.XacMinhThongTinDangNhapId(data, length);
                    break;
                case 218:
                case 211:
                    _player.ChangeLineVerification(data, length);
                    break;
                case 16666:
                    _player.IsAttackConfirmation(data, length);
                    break;
                case 5638:
                case 8212:
                    _player.VersionVerification(data, length);
                    break;
            }
        }

        /// <summary>
        /// Xử lý các packet trong game
        /// </summary>
        private void HandleGamePackets(int packetType, byte[] data, int length)
        {
            // Chuyển tiếp đến phương thức xử lý packet tương ứng trong Players
            // Đây chỉ là một số ví dụ, cần bổ sung đầy đủ các case
            switch (packetType)
            {
                case 14:
                    _player.ThrowItems(data, length);
                    break;
                case 3:
                    _player.DangXuat(data, length);
                    break;
                case 7:
                    _player.CharacterMove(data, length);
                    break;
                case 8:
                    _player.Speak(data, length);
                    break;
                case 9:
                    {
                        _player.Attack(data, length);
                        break;
                    }
                case 11:
                    _player.PickUpItems(data, length);
                    break;
                // Thêm các case khác tương ứng với ManagePacket trong Players
                default:
                    // Chuyển tiếp đến phương thức ManagePacket gốc để xử lý các packet khác
                    _player.ManagePacket(data, length + 2);
                    break;
            }
        }
    }

    /// <summary>
    /// Message yêu cầu xử lý gói tin cho người chơi
    /// </summary>
    public class ProcessPlayerPacket
    {
        public byte[] Data { get; }
        public int Length { get; }

        public ProcessPlayerPacket(byte[] data, int length)
        {
            Data = data;
            Length = length;
        }
    }
}
