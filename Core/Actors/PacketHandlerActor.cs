using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Akka.Actor;
using HeroYulgang.Core.Actors;
using HeroYulgang.Services;
using RxjhServer;

namespace HeroYulgang.Core.Actors
{
    /// <summary>
    /// Actor xử lý các gói tin
    /// </summary>
    public class PacketHandlerActor : ReceiveActor
    {
        private readonly Dictionary<PacketType, Func<IActorRef, ClientSession, Packet, Task>> _handlers = [];

        public PacketHandlerActor()
        {
            // Đăng ký các message handler
            Receive<ProcessPacket>(HandlePacket);
            RegisterHandlers();
        }

        private void RegisterHandlers()
        {
            // Đăng ký các handler cho từng loại gói tin
            //_handlers[PacketType.Ping] = HandlePingAsync;
             _handlers[PacketType.Login] = HandleLoginAsync;
            // _handlers[PacketType.CharacterList] = HandleCharacterListAsync;
            // _handlers[PacketType.CreateCharacter] = HandleCreateCharacterAsync;
            // _handlers[PacketType.DeleteCharacter] = HandleDeleteCharacterAsync;
            // _handlers[PacketType.EnterGame] = HandleEnterGameAsync;
            // _handlers[PacketType.Chat] = HandleChatAsync;
            // _handlers[PacketType.Move] = HandleMoveAsync;
            // _handlers[PacketType.Attack] = HandleAttackAsync;
            // _handlers[PacketType.UseSkill] = HandleUseSkillAsync;
            // _handlers[PacketType.UseItem] = HandleUseItemAsync;
            // _handlers[PacketType.PickupItem] = HandlePickupItemAsync;
            // _handlers[PacketType.DropItem] = HandleDropItemAsync;
            // _handlers[PacketType.EquipItem] = HandleEquipItemAsync;
            // _handlers[PacketType.UnequipItem] = HandleUnequipItemAsync;
            // _handlers[PacketType.TradeRequest] = HandleTradeRequestAsync;
            // _handlers[PacketType.TradeAccept] = HandleTradeAcceptAsync;
            // _handlers[PacketType.TradeCancel] = HandleTradeCancelAsync;
            // _handlers[PacketType.TradeComplete] = HandleTradeCompleteAsync;
            // Đăng ký các handler khác tại đây
        }

        private async Task HandleLoginAsync(IActorRef connection, ClientSession session, Packet packet)
        {
            // Xử lý gói tin Login
            // Giả sử dữ liệu gói tin: [username length][username][password length][password]
            try
            {
                var reader = new BinaryReader(new MemoryStream(packet.Data));
                reader.BaseStream.Seek(14, SeekOrigin.Begin);
                byte[] userNameByte = reader.ReadBytes(14);
                string username = System.Text.Encoding.GetEncoding(World.Language_Charset).GetString(userNameByte).Trim().Replace("\0", String.Empty);

                Logger.Instance.Debug($"Yêu cầu đăng nhập từ {username}");

                // TODO: Xác thực người dùng với cơ sở dữ liệu

                var player = new Players
                {
                    UserName = username,
                    BindAccount = username
                };
                // Giả sử xác thực thành công
                session.IsAuthenticated = true; 
                session.AccountId = username;

                var clientActorPath = $"/user/tcpManager/client-{session.SessionId}";
                var clientActor = Context.ActorSelection(clientActorPath);
                Console.WriteLine(clientActorPath);
                clientActor.Tell(new SetPlayerReference(player));
                Console.WriteLine("Set Actor client");
                // Gui data cho client actor de client actor xu ly dang nhap tiep
                clientActor.Tell(new ProcessPlayerPacket(packet.Data, packet.Data.Length));
                Console.WriteLine("Gui data cho client actor de client actor xu ly dang nhap tiep");
                
                Logger.Instance.Info($"Người dùng {username} đã đăng nhập thành công");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi xử lý gói tin đăng nhập: {ex.Message}");

                // Gửi phản hồi thất bại
                byte[] responseData = BitConverter.GetBytes(0); // 0 = thất bại
                var response = new Packet(PacketType.Login, responseData);
                await SendPacketAsync(connection, response.ToByteArray());
            }
        }


        private async void HandlePacket(ProcessPacket message)
        {
            try
            {
                var packet = Packet.Parse(message.Data, message.Data.Length);

                // Ghi log gói tin đã phân tích
                PacketLogger.LogPacket(message.Session.SessionId, packet, true);

                if (_handlers.TryGetValue(packet.Type, out var handler))
                {
                    await handler(message.Connection, message.Session, packet);
                }
                else
                {
                    Logger.Instance.Warning($"Không có handler cho gói tin loại {packet.Type} từ session {message.Session.SessionId}");
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi xử lý gói tin từ session {message.Session.SessionId}: {ex.Message}");
            }
        }

        // Các phương thức xử lý gói tin

        // Triển khai các phương thức xử lý gói tin khác
        private Task HandleCharacterListAsync(IActorRef connection, ClientSession session, Packet packet) => Task.CompletedTask;
        private Task HandleCreateCharacterAsync(IActorRef connection, ClientSession session, Packet packet) => Task.CompletedTask;
        private Task HandleDeleteCharacterAsync(IActorRef connection, ClientSession session, Packet packet) => Task.CompletedTask;
        private Task HandleEnterGameAsync(IActorRef connection, ClientSession session, Packet packet) => Task.CompletedTask;
        private Task HandleChatAsync(IActorRef connection, ClientSession session, Packet packet) => Task.CompletedTask;
        private Task HandleMoveAsync(IActorRef connection, ClientSession session, Packet packet) => Task.CompletedTask;
        private Task HandleAttackAsync(IActorRef connection, ClientSession session, Packet packet) => Task.CompletedTask;
        private Task HandleUseSkillAsync(IActorRef connection, ClientSession session, Packet packet) => Task.CompletedTask;
        private Task HandleUseItemAsync(IActorRef connection, ClientSession session, Packet packet) => Task.CompletedTask;
        private Task HandlePickupItemAsync(IActorRef connection, ClientSession session, Packet packet) => Task.CompletedTask;
        private Task HandleDropItemAsync(IActorRef connection, ClientSession session, Packet packet) => Task.CompletedTask;
        private Task HandleEquipItemAsync(IActorRef connection, ClientSession session, Packet packet) => Task.CompletedTask;
        private Task HandleUnequipItemAsync(IActorRef connection, ClientSession session, Packet packet) => Task.CompletedTask;
        private Task HandleTradeRequestAsync(IActorRef connection, ClientSession session, Packet packet) => Task.CompletedTask;
        private Task HandleTradeAcceptAsync(IActorRef connection, ClientSession session, Packet packet) => Task.CompletedTask;
        private Task HandleTradeCancelAsync(IActorRef connection, ClientSession session, Packet packet) => Task.CompletedTask;
        private Task HandleTradeCompleteAsync(IActorRef connection, ClientSession session, Packet packet) => Task.CompletedTask;

        // Helper method để gửi gói tin
        private Task SendPacketAsync(IActorRef connection, byte[] data)
        {
            // Gửi gói tin đến connection
            Context.Parent.Tell(new SendPacket(connection, data));

            // Ghi log gói tin gửi đi
            if (data.Length >= 2)
            {
                // Tìm session ID từ connection
                var tcpManager = Context.Parent;
                tcpManager.Tell(new GetSessionIdFromConnection(connection, sessionId =>
                {
                    if (sessionId != -1)
                    {
                        PacketLogger.LogOutgoingPacket(sessionId, data);
                    }
                }));
            }

            return Task.CompletedTask;
        }
    }
}
