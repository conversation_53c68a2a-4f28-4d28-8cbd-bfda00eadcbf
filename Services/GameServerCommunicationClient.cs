using System;
using System.Threading.Tasks;
using System.Threading;
using Grpc.Net.Client;
using HeroYulgang.Protos;
using HeroYulgang.Services;
using System.Linq;
using Grpc.Core;

namespace HeroYulgang.Services
{
    /// <summary>
    /// GRPC Client để giao tiếp với LoginServer
    /// Thay thế cho TCP connection trong Connect.cs
    /// </summary>
    public class GameServerCommunicationClient
    {
        private static GameServerCommunicationClient? _instance;
        private readonly Logger _logger;
        private GrpcChannel? _channel;
        private GameServerCommunication.GameServerCommunicationClient? _client;
        private bool _isConnected;
        private DateTime _lastConnectionAttempt = DateTime.MinValue;
        private readonly TimeSpan _reconnectInterval = TimeSpan.FromSeconds(30);
        private CancellationTokenSource? _streamCancellationTokenSource;

        public bool IsConnected => _isConnected;
        public static GameServerCommunicationClient Instance => _instance ??= new GameServerCommunicationClient();

        private GameServerCommunicationClient()
        {
            _logger = Logger.Instance;
            _isConnected = false;
        }

        /// <summary>
        /// Kết nối đến LoginServer
        /// </summary>
        public async Task<bool> ConnectAsync()
        {
            if (_isConnected)
            {
                return true;
            }

            // Kiểm tra thời gian reconnect
            if (DateTime.Now - _lastConnectionAttempt < _reconnectInterval)
            {
                return false;
            }

            _lastConnectionAttempt = DateTime.Now;

            try
            {
                var loginServerIp = RxjhServer.World.AccountVerificationServerIP;
                var loginServerPort = RxjhServer.World.LoginServerGrpcPort;
                var address = $"http://{loginServerIp}:{loginServerPort}";

                _logger.Info($"Đang kết nối GRPC đến LoginServer tại {address}...");

                // Tạo kênh gRPC
                _channel = GrpcChannel.ForAddress(address);
                _client = new GameServerCommunication.GameServerCommunicationClient(_channel);

                // Test connection bằng cách gửi message connect
                var connectResult = await SendConnectMessage();
                if (!connectResult)
                {
                    throw new Exception("Không thể gửi message kết nối");
                }

                _isConnected = true;
                _logger.Info("Đã kết nối GRPC thành công đến LoginServer");

                // Bắt đầu stream để nhận messages từ LoginServer
                _ = Task.Run(StartReceiveStream);

                return true;
            }
            catch (Exception ex)
            {
                _isConnected = false;
                _logger.Error($"Không thể kết nối GRPC đến LoginServer: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Ngắt kết nối
        /// </summary>
        public async Task DisconnectAsync()
        {
            try
            {
                _isConnected = false;

                // Hủy stream
                _streamCancellationTokenSource?.Cancel();

                // Đóng channel
                if (_channel != null)
                {
                    await _channel.ShutdownAsync();
                    _channel.Dispose();
                    _channel = null;
                }

                _client = null;
                _logger.Info("Đã ngắt kết nối GRPC với LoginServer");
            }
            catch (Exception ex)
            {
                _logger.Error($"Lỗi khi ngắt kết nối GRPC: {ex.Message}");
            }
        }

        /// <summary>
        /// Gửi message đến LoginServer - thay thế cho Transmit()
        /// </summary>
        public async Task<bool> TransmitAsync(string message)
        {
            if (!_isConnected || _client == null)
            {
                _logger.Warning("Chưa kết nối đến LoginServer, không thể gửi message");
                return false;
            }

            try
            {
                // Parse message string thành components
                var parts = message.Split('|');
                if (parts.Length == 0)
                {
                    _logger.Error($"Message format không hợp lệ: {message}");
                    return false;
                }

                var messageType = parts[0];
                var parameters = parts.Skip(1).ToArray();

                var request = new GameServerMessageRequest
                {
                    ServerId = RxjhServer.World.ServerID,
                    ServerName = RxjhServer.World.ServerName,
                    MessageType = messageType,
                    RawMessage = message,
                    Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
                };

                // Thêm parameters
                foreach (var param in parameters)
                {
                    request.Parameters.Add(param);
                }

                // Gửi message
                var response = await _client.SendMessageAsync(request);

                if (!response.Success)
                {
                    _logger.Error($"LoginServer trả về lỗi: {response.Message} (Code: {response.ErrorCode})");
                    return false;
                }

                _logger.Debug($"Đã gửi message thành công: {messageType}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error($"Lỗi khi gửi message: {ex.Message}");
                _isConnected = false; // Đánh dấu mất kết nối để reconnect
                return false;
            }
        }

        /// <summary>
        /// Gửi message và nhận response data
        /// </summary>
        public async Task<(bool Success, string[] ResponseData)> TransmitWithResponseAsync(string message)
        {
            if (!_isConnected || _client == null)
            {
                return (false, Array.Empty<string>());
            }

            try
            {
                var parts = message.Split('|');
                var messageType = parts[0];
                var parameters = parts.Skip(1).ToArray();

                var request = new GameServerMessageRequest
                {
                    ServerId = RxjhServer.World.ServerID,
                    ServerName = RxjhServer.World.ServerName,
                    MessageType = messageType,
                    RawMessage = message,
                    Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
                };

                foreach (var param in parameters)
                {
                    request.Parameters.Add(param);
                }

                var response = await _client.SendMessageWithResponseAsync(request);

                if (!response.Success)
                {
                    _logger.Error($"LoginServer trả về lỗi: {response.Message}");
                    return (false, Array.Empty<string>());
                }

                return (true, response.ResponseData.ToArray());
            }
            catch (Exception ex)
            {
                _logger.Error($"Lỗi khi gửi message với response: {ex.Message}");
                _isConnected = false;
                return (false, Array.Empty<string>());
            }
        }

        /// <summary>
        /// Gửi message kết nối ban đầu
        /// </summary>
        private async Task<bool> SendConnectMessage()
        {
            try
            {
                var connectMessage = $"SERVER_CONNECT_LOGIN_X|{RxjhServer.World.ServerID}|{RxjhServer.World.MaximumOnline}|{RxjhServer.CRC32.GetEXECRC32()}";
                return await TransmitAsync(connectMessage);
            }
            catch (Exception ex)
            {
                _logger.Error($"Lỗi khi gửi message kết nối: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Bắt đầu stream để nhận messages từ LoginServer
        /// </summary>
        private async Task StartReceiveStream()
        {
            try
            {
                _streamCancellationTokenSource = new CancellationTokenSource();

                var request = new GameServerStreamRequest
                {
                    ServerId = RxjhServer.World.ServerID,
                    ServerName = RxjhServer.World.ServerName
                };

                using var call = _client.ReceiveMessages(request, cancellationToken: _streamCancellationTokenSource.Token);

                await foreach (var response in call.ResponseStream.ReadAllAsync(_streamCancellationTokenSource.Token))
                {
                    _logger.Info($"Nhận message từ LoginServer: {response.Message}");
                    // TODO: Xử lý messages từ LoginServer nếu cần
                }
            }
            catch (OperationCanceledException)
            {
                _logger.Info("Stream với LoginServer đã bị hủy");
            }
            catch (Exception ex)
            {
                _logger.Error($"Lỗi trong stream với LoginServer: {ex.Message}");
                _isConnected = false;
            }
        }

        /// <summary>
        /// Kiểm tra và tự động reconnect
        /// </summary>
        public async Task<bool> EnsureConnectedAsync()
        {
            if (_isConnected)
            {
                return true;
            }

            return await ConnectAsync();
        }

        /// <summary>
        /// Dispose resources
        /// </summary>
        public void Dispose()
        {
            _ = Task.Run(async () => await DisconnectAsync());
        }
    }
}
